
import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { ApixGateway } from '../../apix/apix.gateway';
import { AgentType, SkillCategory } from '@prisma/client';

export interface AgentTemplate {
  id: string;
  name: string;
  slug: string;
  config: any;
  description: string;
  category: SkillCategory;
  type: AgentType;
  systemPrompt: string;
  instructions: string;
  provider: string;
  model: string;
  temperature: number;
  maxTokens: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  skills: string[];
  tools: string[];
  capabilities: {
    canCollaborate: boolean;
    shareContext: boolean;
    maxConcurrentTasks?: number;
    memoryWindow: number;
    providerRequirements?: any;
  };
  communication: {
    enableAgentToAgent: boolean;
    allowBroadcast: boolean;
    priority: 'low' | 'normal' | 'high' | 'urgent';
  };
  fallback?: {
    enabled: boolean;
    fallbackAgentId?: string;
    maxRetries: number;
    retryDelay: number;
  };
  monitoring: {
    trackMetrics: boolean;
    logConversations: boolean;
    alertOnErrors: boolean;
  };
  isPublic: boolean;
  tags: string[];
  metadata?: any;
  usageCount: number;
  rating: number;
  createdBy: string;
  organizationId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateTemplateDto {
  name: string;
  config: any;
  description: string;
  category: SkillCategory;
  type: AgentType;
  systemPrompt: string;
  instructions?: string;
  provider: string;
  model: string;
  temperature?: number;
  maxTokens?: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  skills?: string[];
  tools?: string[];
  capabilities?: Partial<AgentTemplate['capabilities']>;
  communication?: Partial<AgentTemplate['communication']>;
  fallback?: Partial<AgentTemplate['fallback']>;
  monitoring?: Partial<AgentTemplate['monitoring']>;
  isPublic?: boolean;
  tags?: string[];
  metadata?: any;
}

@Injectable()
export class AgentTemplateService {
  private readonly logger = new Logger(AgentTemplateService.name);

  constructor(
    private prisma: PrismaService,
    private apixGateway: ApixGateway,
  ) {
    this.initializeDefaultTemplates();
  }

  private async initializeDefaultTemplates() {
    try {
      // Check if default templates already exist
      const existingTemplates = await this.prisma.agentTemplate.count({
        where: { isPublic: true }
      });

      if (existingTemplates === 0) {
        await this.createDefaultTemplates();
      }
    } catch (error) {
      this.logger.error('Failed to initialize default templates', error);
    }
  }

  private async createDefaultTemplates() {
    const defaultTemplates = [
      {
        name: "Customer Support Agent",
        description: "Specialized agent for handling customer inquiries, complaints, and support requests with empathy and efficiency",
        category: "Support",
        type: AgentType.TOOL_DRIVEN,
        systemPrompt: `You are a professional customer support agent. Your role is to:
- Provide helpful, accurate, and timely responses to customer inquiries
- Show empathy and understanding for customer concerns
- Escalate complex issues when necessary
- Maintain a friendly and professional tone
- Use available tools to search for information and create tickets
- Follow company policies and procedures`,
        instructions: `Guidelines:
1. Always greet customers warmly
2. Listen actively to their concerns
3. Ask clarifying questions when needed
4. Provide step-by-step solutions
5. Confirm customer satisfaction before closing
6. Document all interactions properly`,
        provider: "openai",
        model: "gpt-4",
        temperature: 0.7,
        maxTokens: 2000,
        skills: ["customer_service", "problem_solving", "communication", "empathy"],
        tools: ["search_tool", "ticket_system", "knowledge_base"],
        capabilities: {
          canCollaborate: true,
          shareContext: true,
          memoryWindow: 20,
        },
        communication: {
          enableAgentToAgent: true,
          allowBroadcast: false,
          priority: "high",
        },
        monitoring: {
          trackMetrics: true,
          logConversations: true,
          alertOnErrors: true,
        },
        isPublic: true,
        tags: ["customer-service", "support", "communication"],
      },
      {
        name: "Data Analysis Agent",
        description: "Expert agent for analyzing data, generating insights, and creating visualizations from complex datasets",
        category: "Analytics",
        type: AgentType.HYBRID,
        systemPrompt: `You are a data analysis expert. Your responsibilities include:
- Analyzing complex datasets and identifying patterns
- Creating clear, actionable insights from data
- Generating visualizations and reports
- Explaining statistical concepts in simple terms
- Recommending data-driven decisions
- Ensuring data accuracy and integrity`,
        instructions: `Analysis approach:
1. Understand the business context and objectives
2. Examine data quality and completeness
3. Apply appropriate statistical methods
4. Create meaningful visualizations
5. Provide clear, actionable recommendations
6. Document methodology and assumptions`,
        provider: "claude",
        model: "claude-3-opus",
        temperature: 0.3,
        maxTokens: 4000,
        skills: ["data_analysis", "statistics", "visualization", "reporting"],
        tools: ["database_tool", "chart_generator", "statistical_analysis", "excel_processor"],
        capabilities: {
          canCollaborate: false,
          shareContext: false,
          memoryWindow: 30,
        },
        communication: {
          enableAgentToAgent: false,
          allowBroadcast: false,
          priority: "normal",
        },
        monitoring: {
          trackMetrics: true,
          logConversations: true,
          alertOnErrors: true,
        },
        isPublic: true,
        tags: ["data-analysis", "statistics", "visualization", "reporting"],
      },
      {
        name: "Code Review Agent",
        description: "Specialized agent for reviewing code quality, security vulnerabilities, and best practices compliance",
        category: "Development",
        type: AgentType.TOOL_DRIVEN,
        systemPrompt: `You are a senior software engineer specializing in code review. Your tasks include:
- Reviewing code for quality, readability, and maintainability
- Identifying security vulnerabilities and potential bugs
- Ensuring adherence to coding standards and best practices
- Providing constructive feedback and improvement suggestions
- Checking for performance optimizations
- Validating test coverage and documentation`,
        instructions: `Review checklist:
1. Code structure and organization
2. Naming conventions and clarity
3. Security vulnerabilities
4. Performance considerations
5. Error handling and edge cases
6. Test coverage and quality
7. Documentation completeness
8. Compliance with team standards`,
        provider: "openai",
        model: "gpt-4",
        temperature: 0.2,
        maxTokens: 3000,
        skills: ["code_review", "security_analysis", "performance_optimization", "testing"],
        tools: ["code_analyzer", "security_scanner", "test_coverage", "documentation_checker"],
        capabilities: {
          canCollaborate: true,
          shareContext: false,
          memoryWindow: 15,
        },
        communication: {
          enableAgentToAgent: true,
          allowBroadcast: true,
          priority: "normal",
        },
        monitoring: {
          trackMetrics: true,
          logConversations: true,
          alertOnErrors: true,
        },
        isPublic: true,
        tags: ["code-review", "security", "development", "quality-assurance"],
      },
      {
        name: "Content Creation Agent",
        description: "Creative agent for generating high-quality content including articles, marketing copy, and social media posts",
        category: "Content",
        type: AgentType.STANDALONE,
        systemPrompt: `You are a professional content creator and copywriter. Your expertise includes:
- Creating engaging, original content for various platforms
- Adapting tone and style to target audiences
- Optimizing content for SEO and engagement
- Maintaining brand voice and consistency
- Researching topics thoroughly
- Creating compelling headlines and calls-to-action`,
        instructions: `Content creation process:
1. Understand the target audience and objectives
2. Research the topic thoroughly
3. Create an outline or structure
4. Write engaging, original content
5. Optimize for the intended platform
6. Review for tone, clarity, and accuracy
7. Include relevant keywords naturally
8. Add compelling headlines and CTAs`,
        provider: "openai",
        model: "gpt-4",
        temperature: 0.8,
        maxTokens: 3000,
        skills: ["content_writing", "copywriting", "seo", "marketing"],
        tools: ["research_tool", "seo_analyzer", "plagiarism_checker"],
        capabilities: {
          canCollaborate: false,
          shareContext: false,
          memoryWindow: 10,
        },
        communication: {
          enableAgentToAgent: false,
          allowBroadcast: false,
          priority: "normal",
        },
        monitoring: {
          trackMetrics: true,
          logConversations: true,
          alertOnErrors: false,
        },
        isPublic: true,
        tags: ["content-creation", "copywriting", "marketing", "seo"],
      },
      {
        name: "Project Management Agent",
        description: "Organizational agent for managing projects, tracking tasks, and coordinating team activities",
        category: "Management",
        type: AgentType.MULTI_TASKING,
        systemPrompt: `You are an experienced project manager. Your responsibilities include:
- Planning and organizing project activities
- Tracking progress and identifying bottlenecks
- Coordinating team members and resources
- Managing timelines and deadlines
- Communicating with stakeholders
- Risk assessment and mitigation
- Quality assurance and deliverable review`,
        instructions: `Project management approach:
1. Define clear project scope and objectives
2. Create detailed project plans and timelines
3. Assign tasks and responsibilities
4. Monitor progress regularly
5. Communicate updates to stakeholders
6. Identify and address risks proactively
7. Ensure quality standards are met
8. Document lessons learned`,
        provider: "openai",
        model: "gpt-4",
        temperature: 0.5,
        maxTokens: 2500,
        skills: ["project_management", "planning", "coordination", "communication"],
        tools: ["task_manager", "calendar", "reporting_tool", "communication_platform"],
        capabilities: {
          canCollaborate: true,
          shareContext: true,
          maxConcurrentTasks: 5,
          memoryWindow: 25,
        },
        communication: {
          enableAgentToAgent: true,
          allowBroadcast: true,
          priority: "high",
        },
        monitoring: {
          trackMetrics: true,
          logConversations: true,
          alertOnErrors: true,
        },
        isPublic: true,
        tags: ["project-management", "planning", "coordination", "leadership"],
      },
      {
        name: "Research Assistant Agent",
        description: "Academic and business research agent for gathering information, analyzing sources, and synthesizing findings",
        category: "Research",
        type: AgentType.HYBRID,
        systemPrompt: `You are a research assistant with expertise in information gathering and analysis. Your role involves:
- Conducting thorough research on various topics
- Evaluating source credibility and reliability
- Synthesizing information from multiple sources
- Creating comprehensive research reports
- Identifying knowledge gaps and research opportunities
- Maintaining proper citations and references`,
        instructions: `Research methodology:
1. Define research questions and objectives
2. Identify relevant sources and databases
3. Evaluate source credibility and bias
4. Gather and organize information systematically
5. Analyze and synthesize findings
6. Identify patterns and insights
7. Create structured reports with citations
8. Recommend further research directions`,
        provider: "claude",
        model: "claude-3-sonnet",
        temperature: 0.4,
        maxTokens: 4000,
        skills: ["research", "analysis", "writing", "critical_thinking"],
        tools: ["search_engine", "academic_database", "citation_manager", "document_analyzer"],
        capabilities: {
          canCollaborate: false,
          shareContext: false,
          memoryWindow: 40,
        },
        communication: {
          enableAgentToAgent: false,
          allowBroadcast: false,
          priority: "normal",
        },
        monitoring: {
          trackMetrics: true,
          logConversations: true,
          alertOnErrors: false,
        },
        isPublic: true,
        tags: ["research", "analysis", "academic", "information-gathering"],
      },
      {
        name: "Sales Assistant Agent",
        description: "Sales-focused agent for lead qualification, customer engagement, and sales process optimization",
        category: "Sales",
        type: AgentType.TOOL_DRIVEN,
        systemPrompt: `You are a professional sales assistant. Your objectives are to:
- Qualify leads and identify potential customers
- Engage prospects with personalized communication
- Understand customer needs and pain points
- Present solutions that match customer requirements
- Handle objections professionally
- Guide prospects through the sales funnel
- Maintain accurate records of all interactions`,
        instructions: `Sales process:
1. Qualify leads based on defined criteria
2. Research prospect background and needs
3. Initiate personalized outreach
4. Conduct discovery conversations
5. Present tailored solutions
6. Address concerns and objections
7. Guide toward decision-making
8. Follow up consistently
9. Update CRM with all activities`,
        provider: "openai",
        model: "gpt-4",
        temperature: 0.6,
        maxTokens: 2500,
        skills: ["sales", "communication", "persuasion", "relationship_building"],
        tools: ["crm_system", "email_platform", "lead_scorer", "proposal_generator"],
        capabilities: {
          canCollaborate: true,
          shareContext: true,
          memoryWindow: 20,
        },
        communication: {
          enableAgentToAgent: true,
          allowBroadcast: false,
          priority: "high",
        },
        monitoring: {
          trackMetrics: true,
          logConversations: true,
          alertOnErrors: true,
        },
        isPublic: true,
        tags: ["sales", "lead-generation", "customer-engagement", "crm"],
      },
      {
        name: "Technical Documentation Agent",
        description: "Specialized agent for creating, maintaining, and updating technical documentation and user guides",
        category: "Documentation",
        type: AgentType.STANDALONE,
        systemPrompt: `You are a technical writer specializing in documentation. Your expertise includes:
- Creating clear, comprehensive technical documentation
- Writing user guides and tutorials
- Maintaining API documentation
- Developing troubleshooting guides
- Ensuring documentation accuracy and completeness
- Adapting content for different technical skill levels
- Following documentation standards and best practices`,
        instructions: `Documentation standards:
1. Use clear, concise language
2. Structure content logically
3. Include relevant examples and code snippets
4. Add screenshots and diagrams where helpful
5. Maintain consistent formatting and style
6. Test all procedures and examples
7. Keep content up-to-date
8. Include proper cross-references and links`,
        provider: "openai",
        model: "gpt-4",
        temperature: 0.3,
        maxTokens: 3500,
        skills: ["technical_writing", "documentation", "editing", "communication"],
        tools: ["documentation_platform", "screenshot_tool", "diagram_creator", "version_control"],
        capabilities: {
          canCollaborate: false,
          shareContext: false,
          memoryWindow: 15,
        },
        communication: {
          enableAgentToAgent: false,
          allowBroadcast: false,
          priority: "normal",
        },
        monitoring: {
          trackMetrics: true,
          logConversations: true,
          alertOnErrors: false,
        },
        isPublic: true,
        tags: ["documentation", "technical-writing", "user-guides", "api-docs"],
      }
    ];

    // Create default templates
    for (const template of defaultTemplates) {
      try {
        await this.prisma.agentTemplate.create({
          data: {
            name: template.name,
            description: template.description,
            category: template.category as SkillCategory,
            type: template.type as AgentType,
            config: template.config,
            systemPrompt: template.systemPrompt,
            instructions: template.instructions,
            provider: template.provider,
            model: template.model,
            temperature: template.temperature,
            maxTokens: template.maxTokens,
            skills: template.skills,
            tools: template.tools,
            capabilities: template.capabilities,
            communication: template.communication,
            monitoring: template.monitoring,
            tags: template.tags,
            isPublic: true,
            createdBy: 'system',
            organizationId: 'system',
          }
        });
      } catch (error) {
        this.logger.error(`Failed to create default template: ${template.name}`, error);
      }
    }

    this.logger.log(`Created ${defaultTemplates.length} default agent templates`);
  }

  async createTemplate(
    createDto: CreateTemplateDto,
    createdBy: string,
    organizationId: string
  ): Promise<AgentTemplate> {
    const template = await this.prisma.agentTemplate.create({
      data: {
        name: createDto.name,
        description: createDto.description,
        category: createDto.category as SkillCategory,
        systemPrompt: createDto.systemPrompt,
        instructions: createDto.instructions || '',
        skills: createDto.skills || [],
        isPublic: createDto.isPublic || false,
        tags: createDto.tags || [],
        createdBy,
        organizationId,
        // Store AI model configuration and other settings in the config field as JSON
        config: {
          type: createDto.type,
          provider: createDto.provider,
          model: createDto.model,
          temperature: createDto.temperature || 0.7,
          maxTokens: createDto.maxTokens || 2000,
          topP: createDto.topP || 0.9,
          frequencyPenalty: createDto.frequencyPenalty || 0,
          presencePenalty: createDto.presencePenalty || 0,
          tools: createDto.tools || [],
          capabilities: createDto.capabilities || {},
          communication: createDto.communication || {},
          fallback: createDto.fallback || {},
          monitoring: createDto.monitoring || {},
          metadata: createDto.metadata || {},
          ...createDto.config, // Allow additional config from the DTO
        },
      }
    });

    // Emit template created event
    await this.apixGateway.emitToOrganization(
      organizationId,
      'agent_template_created',
      {
        templateId: template.id,
        name: template.name,
        category: template.category,
        createdBy,
      }
    );

    this.logger.log(`Agent template created: ${template.name} by ${createdBy}`);

    return template as unknown as AgentTemplate;
  }

  async getTemplates(
    organizationId: string,
    filters?: {
      category?: string;
      type?: AgentType;
      isPublic?: boolean;
      search?: string;
      tags?: string[];
      limit?: number;
      offset?: number;
    }
  ): Promise<{ templates: AgentTemplate[]; total: number }> {
    const where: any = {
      OR: [
        { organizationId },
        { isPublic: true },
        { organizationId: 'system' }
      ]
    };

    if (filters?.category) {
      where.category = filters.category;
    }

    if (filters?.type) {
      where.type = filters.type;
    }

    if (filters?.isPublic !== undefined) {
      where.isPublic = filters.isPublic;
    }

    if (filters?.search) {
      where.OR = [
        { name: { contains: filters.search, mode: 'insensitive' } },
        { description: { contains: filters.search, mode: 'insensitive' } },
      ];
    }

    if (filters?.tags && filters.tags.length > 0) {
      where.tags = {
        hasSome: filters.tags,
      };
    }

    const [templates, total] = await Promise.all([
      this.prisma.agentTemplate.findMany({
        where,
        include: {
          creator: {
            select: { id: true, firstName: true, lastName: true, email: true }
          },
          _count: {
            select: { instances: true }
          }
        },
        orderBy: [
          { createdAt: 'desc' }
        ],
        take: filters?.limit || 20,
        skip: filters?.offset || 0,
      }),
      this.prisma.agentTemplate.count({ where })
    ]);

    return {
      templates: templates as unknown as AgentTemplate[],
      total
    };
  }

  async getTemplate(
    templateId: string,
    organizationId: string
  ): Promise<AgentTemplate | null> {
    const template = await this.prisma.agentTemplate.findFirst({
      where: {
        id: templateId,
        OR: [
          { organizationId },
          { isPublic: true },
          { organizationId: 'system' }
        ]
      },
      include: {
        creator: {
          select: { id: true, firstName: true, lastName: true, email: true }
        },
        instances: {
          take: 5,
          orderBy: { createdAt: 'desc' },
          select: {
            id: true,
            name: true,
            status: true,
            createdAt: true,
            creator: {
              select: { id: true, firstName: true, lastName: true }
            }
          }
        },
        _count: {
          select: { instances: true }
        }
      }
    });

    return template as unknown as AgentTemplate | null;
  }

  async updateTemplate(
    templateId: string,
    updateDto: Partial<CreateTemplateDto>,
    userId: string,
    organizationId: string
  ): Promise<AgentTemplate> {
    // Check if user can update this template
    const template = await this.prisma.agentTemplate.findFirst({
      where: {
        id: templateId,
        OR: [
          { createdBy: userId },
          { organizationId }
        ]
      }
    });

    if (!template) {
      throw new Error('Template not found or access denied');
    }

    const updatedTemplate = await this.prisma.agentTemplate.update({
      where: { id: templateId },
      data: {
        // Only update fields that exist in the AgentTemplate model
        ...(updateDto.name && { name: updateDto.name }),
        ...(updateDto.description && { description: updateDto.description }),
        ...(updateDto.category && { category: updateDto.category }),
        ...(updateDto.systemPrompt !== undefined && { systemPrompt: updateDto.systemPrompt }),
        ...(updateDto.instructions !== undefined && { instructions: updateDto.instructions }),
        ...(updateDto.skills && { skills: updateDto.skills }),
        ...(updateDto.tags && { tags: updateDto.tags }),
        ...(updateDto.isPublic !== undefined && { isPublic: updateDto.isPublic }),
        updatedAt: new Date(),
      }
    });

    // Emit template updated event
    await this.apixGateway.emitToOrganization(
      organizationId,
      'agent_template_updated',
      {
        templateId,
        name: updatedTemplate.name,
        updatedBy: userId,
      }
    );

    return updatedTemplate as unknown as AgentTemplate;
  }

  async deleteTemplate(
    templateId: string,
    userId: string,
    organizationId: string
  ): Promise<void> {
    // Check if user can delete this template
    const template = await this.prisma.agentTemplate.findFirst({
      where: {
        id: templateId,
        createdBy: userId,
        organizationId,
        isPublic: true, // Cannot delete default templates
      }
    });

    if (!template) {
      throw new Error('Template not found or cannot be deleted');
    }

    // Check if template is being used
    const instanceCount = await this.prisma.agentInstance.count({
      where: { templateId }
    });

    if (instanceCount > 0) {
      throw new Error('Cannot delete template that is being used by agent instances');
    }

    await this.prisma.agentTemplate.delete({
      where: { id: templateId }
    });

    // Emit template deleted event
    await this.apixGateway.emitToOrganization(
      organizationId,
      'agent_template_deleted',
      {
        templateId,
        name: template.name,
        deletedBy: userId,
      }
    );

    this.logger.log(`Agent template deleted: ${template.name} by ${userId}`);
  }

  async incrementUsageCount(templateId: string): Promise<void> {
    await this.prisma.agentTemplate.update({
      where: { id: templateId },
      data: {
        updatedAt: new Date()
      }
    });
  }

  async rateTemplate(
    templateId: string,
    rating: number,
    userId: string,
    organizationId: string
  ): Promise<void> {
    // Validate rating
    if (rating < 1 || rating > 5) {
      throw new Error('Rating must be between 1 and 5');
    }

    // Check if template exists and is accessible
    const template = await this.prisma.agentTemplate.findFirst({
      where: {
        id: templateId,
        OR: [
          { organizationId },
          { isPublic: true },
          { organizationId: 'system' }
        ]
      }
    });

    if (!template) {
      throw new Error('Template not found');
    }

    // Create or update rating
    // TODO: Add agentTemplateRating model to Prisma schema
    // For now, we'll skip the rating functionality
    /*
    await this.prisma.agentTemplateRating.upsert({
      where: {
        templateId_userId: {
          templateId,
          userId
        }
      },
      update: {
        rating,
        updatedAt: new Date(),
      },
      create: {
        templateId,
        userId,
        rating,
      }
        });
        */

    // Set a default rating for now
    await this.prisma.agentTemplate.update({
      where: { id: templateId },
      data: { rating: rating }
    });
  }

  async getCategories(organizationId: string): Promise<string[]> {
    const categories = await this.prisma.agentTemplate.findMany({
      where: {
        OR: [
          { organizationId },
          { isPublic: true },
          { organizationId: 'system' }
        ]
      },
      select: { category: true },
      distinct: ['category']
    });

    return categories.map(c => c.category).sort();
  }

  async getPopularTemplates(
    organizationId: string,
    limit: number = 10
  ): Promise<AgentTemplate[]> {
    const templates = await this.prisma.agentTemplate.findMany({
      where: {
        OR: [
          { organizationId },
          { isPublic: true },
          { organizationId: 'system' }
        ]
      },
      include: {
        creator: {
          select: { id: true, firstName: true, lastName: true }
        },
        _count: {
          select: { instances: true }
        }
      },
      orderBy: [
        { updatedAt: 'desc' }
      ],
      take: limit
    });

    return templates as unknown as AgentTemplate[];
  }

  async searchTemplates(
    query: string,
    organizationId: string,
    filters?: {
      category?: string;
      type?: AgentType;
      tags?: string[];
    }
  ): Promise<AgentTemplate[]> {
    const where: any = {
      OR: [
        { organizationId },
        { isPublic: true },
        { organizationId: 'system' }
      ],
      AND: [
        {
          OR: [
            { name: { contains: query, mode: 'insensitive' } },
            { description: { contains: query, mode: 'insensitive' } },
            { systemPrompt: { contains: query, mode: 'insensitive' } },
            { tags: { hasSome: [query] } }
          ]
        }
      ]
    };

    if (filters?.category) {
      where.AND.push({ category: filters.category });
    }

    if (filters?.type) {
      where.AND.push({ type: filters.type });
    }

    if (filters?.tags && filters.tags.length > 0) {
      where.AND.push({ tags: { hasSome: filters.tags } });
    }

    const templates = await this.prisma.agentTemplate.findMany({
      where,
      include: {
        creator: {
          select: { id: true, firstName: true, lastName: true }
        },
        _count: {
          select: { instances: true }
        }
      },
      orderBy: [
        { updatedAt: 'desc' },
        { rating: 'desc' }
      ],
      take: 20
    });

    return templates as unknown as AgentTemplate[];
  }
}
